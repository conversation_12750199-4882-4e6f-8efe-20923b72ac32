#Description For The Start of My Multiplication Game#
print("Welcome to Multiplication Magic!")
print("This game is to build your confidence in your math timestables and to overall improve!")
#End of Description For Game

#This Input will ask for the players name to store#
name = input("What's your name? ") #Name Input
print(f"Hi {name}, are you ready to play?") #This Print Statement will display the players name using "{name}" and ask if there ready to play#

ready = input("Type 'yes' to start or 'no' to quit: ").strip().lower() #This input will perform the question for if they want to play the game or not
while ready != "yes": #If Player Selects Yes the game will start        
    if ready == "no": # If Player Selects no the game will end
        print("Okay, see you next time!") # Will Print the message if player ended the game
        exit() #This is exiting the game. Basically just stopping the game no continuing unless the player starts the game (Im Looking at adding so if its no it will return player to start menu of the game to continue making it easy and more user friendly.)
    ready = input("Please type 'yes' to play or 'no' to quit: ").strip().lower() #End of this

print("Great! Let's begin.\n") # This will display if the player selects yes in the game and actually start the game

print("What time tables would you like to practise?") # This will ask the player what times tables they want to practise 
timetable = input("1x, 2x, 3x, 4x, 5x, 6x, 7x, 8x, 9x, 10x, 11x, 12x,") # This input is where the player will select what they have chosen
print(f"Nice choice, {name}! Let's start with the {timetable} table.") # This will print the players name using {name} and will also print the timestables the player has selected by using {timetable}
# End of code for now - Next lesson will be working on adding the multiplication Functionality and making a proper start to my game

